package com.fi.seranoah;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

import com.fi.seranoah.handler.ControllerInterceptor;

@SpringBootApplication
@ComponentScan(basePackages={"com.fi.seranoah"})
public class FleetupApplication {

	public static void main(String[] args) {
		SpringApplication.run(FleetupApplication.class, args);
	}
	
	@Bean
    public WebMvcConfigurerAdapter webMvcConfigurerAdapter() {
        return new WebMvcConfigurerAdapter() {
            @Override
            public void addInterceptors(InterceptorRegistry registry) {
//                registry.addInterceptor(new ControllerInterceptor());
            }
        };
    }
}
